package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.R;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

public class CategoryEditBottomSheetDialogFragment extends BottomSheetDialogFragment {

    // UI组件
    private TextView titleTextView;
    private ImageView closeButton;
    private EditText nameEditText;
    private RadioGroup typeRadioGroup;
    private RadioButton expenseRadio;
    private RadioButton incomeRadio;
    private RadioButton transferRadio;
    private Button cancelButton;
    private Button saveButton;

    // 数据
    private TransactionCategory editingCategory;
    private TransactionCategory parentCategory;
    private TransactionSubcategory editingSubcategory;
    private int defaultType;
    private OnCategorySaveListener listener;

    public interface OnCategorySaveListener {
        void onCategorySaved(TransactionCategory category, TransactionSubcategory subcategory, boolean isEdit);
    }

    public static CategoryEditBottomSheetDialogFragment newInstance(
            TransactionCategory category, 
            TransactionCategory parentCategory, 
            int defaultType) {
        CategoryEditBottomSheetDialogFragment fragment = new CategoryEditBottomSheetDialogFragment();
        Bundle args = new Bundle();
        if (category != null) {
            args.putSerializable("editing_category", category);
        }
        if (parentCategory != null) {
            args.putSerializable("parent_category", parentCategory);
        }
        args.putInt("default_type", defaultType);
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_category_edit_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        parseArguments();
        initViews(view);
        setupClickListeners();
        populateData();
    }

    private void parseArguments() {
        if (getArguments() != null) {
            editingCategory = (TransactionCategory) getArguments().getSerializable("editing_category");
            parentCategory = (TransactionCategory) getArguments().getSerializable("parent_category");
            defaultType = getArguments().getInt("default_type", 0);
        }
    }

    private void initViews(View view) {
        titleTextView = view.findViewById(R.id.category_edit_title);
        closeButton = view.findViewById(R.id.category_edit_close);
        nameEditText = view.findViewById(R.id.category_edit_name);
        typeRadioGroup = view.findViewById(R.id.category_edit_type_group);
        expenseRadio = view.findViewById(R.id.category_edit_expense);
        incomeRadio = view.findViewById(R.id.category_edit_income);
        transferRadio = view.findViewById(R.id.category_edit_transfer);
        cancelButton = view.findViewById(R.id.category_edit_cancel);
        saveButton = view.findViewById(R.id.category_edit_save);
    }

    private void setupClickListeners() {
        closeButton.setOnClickListener(v -> dismiss());
        cancelButton.setOnClickListener(v -> dismiss());
        saveButton.setOnClickListener(v -> saveCategory());
    }

    private void populateData() {
        if (parentCategory != null) {
            // 编辑子分类
            titleTextView.setText("编辑子分类");
            typeRadioGroup.setVisibility(View.GONE);
            
            if (editingSubcategory != null) {
                nameEditText.setText(editingSubcategory.getSubcategoryName());
            }
        } else if (editingCategory != null) {
            // 编辑主分类
            titleTextView.setText("编辑分类");
            nameEditText.setText(editingCategory.getCategoryName());
            
            // 设置类型
            switch (editingCategory.getCategoryType()) {
                case 0:
                    expenseRadio.setChecked(true);
                    break;
                case 1:
                    incomeRadio.setChecked(true);
                    break;
                case 2:
                    transferRadio.setChecked(true);
                    break;
            }
        } else {
            // 新增分类
            if (parentCategory != null) {
                titleTextView.setText("新增子分类");
                typeRadioGroup.setVisibility(View.GONE);
            } else {
                titleTextView.setText("新增分类");
                
                // 设置默认类型
                switch (defaultType) {
                    case 0:
                        expenseRadio.setChecked(true);
                        break;
                    case 1:
                        incomeRadio.setChecked(true);
                        break;
                    case 2:
                        transferRadio.setChecked(true);
                        break;
                }
            }
        }
    }

    private void saveCategory() {
        String name = nameEditText.getText().toString().trim();
        if (name.isEmpty()) {
            Toast.makeText(getContext(), "请输入名称", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            if (parentCategory != null) {
                // 保存子分类
                TransactionSubcategory subcategory = editingSubcategory != null ? 
                        editingSubcategory : new TransactionSubcategory();
                
                subcategory.setSubcategoryName(name);
                subcategory.setParentCategoryId(parentCategory.getCategoryId());
                subcategory.setSubcategoryIcon(R.drawable.ic_category);
                
                if (listener != null) {
                    listener.onCategorySaved(null, subcategory, editingSubcategory != null);
                }
            } else {
                // 保存主分类
                TransactionCategory category = editingCategory != null ? 
                        editingCategory : new TransactionCategory();
                
                category.setCategoryName(name);
                category.setCategoryIcon(R.drawable.ic_category);
                
                // 获取选中的类型
                int selectedId = typeRadioGroup.getCheckedRadioButtonId();
                if (selectedId == R.id.category_edit_expense) {
                    category.setCategoryType(0);
                } else if (selectedId == R.id.category_edit_income) {
                    category.setCategoryType(1);
                } else if (selectedId == R.id.category_edit_transfer) {
                    category.setCategoryType(2);
                }
                
                if (listener != null) {
                    listener.onCategorySaved(category, null, editingCategory != null);
                }
            }
            
            dismiss();
            
        } catch (Exception e) {
            Toast.makeText(getContext(), "保存失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    public void setOnCategorySaveListener(OnCategorySaveListener listener) {
        this.listener = listener;
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }
}
