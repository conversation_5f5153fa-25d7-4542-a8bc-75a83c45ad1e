package com.example.likeqianwang.widgets;

import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.likeqianwang.Dao.BudgetDao;
import com.example.likeqianwang.Entity.Budget;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.BudgetViewModel;
import com.example.likeqianwang.ViewModel.ReceiptViewModel;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.List;

public class Widget_Budget extends Fragment {
    private BudgetViewModel budgetViewModel;
    private ReceiptViewModel receiptViewModel;
    private DecimalFormat amountFormatter;

    // UI组件
    private TextView remainingBudgetLabel;
    private TextView remainingBudgetAmount;
    private TextView usedBudgetLabel;
    private TextView usagePercentage;
    private ProgressBar budgetProgressBar;
    private TextView usedAmount;
    private TextView totalBudgetAmount;
    private View statusIndicator;
    private TextView statusText;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.widget_budget, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        initViewModel();
        observeBudgetData();
    }

    private void initViews(View view) {
        remainingBudgetLabel = view.findViewById(R.id.widget_budget_剩余预算);
        remainingBudgetAmount = view.findViewById(R.id.widget_budget_剩余预算金额);
        usedBudgetLabel = view.findViewById(R.id.widget_budget_已用预算);
        usagePercentage = view.findViewById(R.id.widget_budget_使用百分比);
        budgetProgressBar = view.findViewById(R.id.widget_budget_进度条);
        usedAmount = view.findViewById(R.id.widget_budget_已用金额);
        totalBudgetAmount = view.findViewById(R.id.widget_budget_总预算金额);
        statusIndicator = view.findViewById(R.id.widget_budget_状态指示器);
        statusText = view.findViewById(R.id.widget_budget_状态文本);

        // 初始化格式化工具
        amountFormatter = new DecimalFormat("¥#,##0.00");

        // 初始化进度条
        initProgressBar();
    }

    private void initViewModel() {
        budgetViewModel = new ViewModelProvider(requireActivity()).get(BudgetViewModel.class);
        receiptViewModel = new ViewModelProvider(requireActivity()).get(ReceiptViewModel.class);
    }

    private void initProgressBar() {
        // 设置进度条的基本属性
        budgetProgressBar.setMax(100);
        budgetProgressBar.setProgress(0);

        // 决定是否使用自定义drawable
        boolean useCustomDrawable = true; // 设置为false以使用默认样式

        if (useCustomDrawable) {
            // 使用兼容的方式设置自定义drawable
            try {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar, getContext().getTheme()));
                } else {
                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar));
                }
                Log.d("BudgetWidget", "Custom progress bar drawable set successfully");
            } catch (Exception e) {
                Log.e("BudgetWidget", "Failed to set custom drawable, using default", e);
                // 如果设置自定义drawable失败，保持默认样式
            }
        } else {
            Log.d("BudgetWidget", "Using default progress bar style");
        }

        Log.d("BudgetWidget", "Progress bar initialized");
    }

    private void observeBudgetData() {
        // 监听ReceiptViewModel中的月份变化
        receiptViewModel.getCurrentYearMonth().observe(getViewLifecycleOwner(), date -> {
            if (date != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                int year = calendar.get(Calendar.YEAR);
                int month = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based

                // 观察总预算
                budgetViewModel.getTotalBudget("MONTHLY", year, month)
                        .observe(getViewLifecycleOwner(), this::updateTotalBudgetDisplay);

                // 观察预算使用情况
                budgetViewModel.getBudgetUsage("MONTHLY", year, month)
                        .observe(getViewLifecycleOwner(), this::updateBudgetUsageDisplay);
            }
        });
    }

    private void updateTotalBudgetDisplay(Budget totalBudget) {
        if (totalBudget != null) {
            totalBudgetAmount.setText(amountFormatter.format(totalBudget.getBudgetAmount()));
        } else {
            // 没有设置总预算时显示默认信息
            totalBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));
            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));
            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));
            usagePercentage.setText("0%");

            // 重置进度条
            budgetProgressBar.setProgress(0);
            budgetProgressBar.getProgressDrawable().setColorFilter(Color.GRAY, android.graphics.PorterDuff.Mode.SRC_IN);

            updateStatusDisplay("未设置预算", Color.GRAY);
        }
    }

    private void updateBudgetUsageDisplay(List<BudgetDao.BudgetUsage> budgetUsages) {
        if (budgetUsages == null || budgetUsages.isEmpty()) {
            // 没有预算使用数据
            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));
            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));
            usagePercentage.setText("0%");
            budgetProgressBar.setProgress(0);
            updateStatusDisplay("无数据", Color.GRAY);
            return;
        }

        // 查找总预算的使用情况
        BudgetDao.BudgetUsage totalBudgetUsage = null;
        for (BudgetDao.BudgetUsage usage : budgetUsages) {
            if ("TOTAL".equals(usage.budgetType)) {
                totalBudgetUsage = usage;
                break;
            }
        }

        if (totalBudgetUsage != null) {
            updateBudgetDisplay(totalBudgetUsage);
        } else {
            // 如果没有总预算，计算所有分类预算的总和
            calculateCombinedBudgetUsage(budgetUsages);
        }
    }

    private void updateBudgetDisplay(BudgetDao.BudgetUsage budgetUsage) {
        BigDecimal budgetAmount = budgetUsage.budgetAmount != null ? budgetUsage.budgetAmount : BigDecimal.ZERO;
        BigDecimal usedAmountValue = budgetUsage.usedAmount != null ? budgetUsage.usedAmount : BigDecimal.ZERO;
        BigDecimal remainingAmount = budgetAmount.subtract(usedAmountValue);

        // 更新金额显示
        remainingBudgetAmount.setText(amountFormatter.format(remainingAmount));
        usedAmount.setText(amountFormatter.format(usedAmountValue));
        totalBudgetAmount.setText(amountFormatter.format(budgetAmount));

        // 更新百分比和进度条
        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);
        int progressValue = (int) remainingPercentage;
        Log.d("BudgetWidget", "Remaining percentage: " + remainingPercentage + ", Progress value: " + progressValue);
        usagePercentage.setText(String.format("%.0f%%", remainingPercentage));

        // 更新进度条 - 使用更可靠的方法
        updateProgressBar(progressValue, budgetUsage);

        // 将"已用预算"改为"剩余预算百分比"
        usedBudgetLabel.setText("剩余预算百分比");

        // 更新状态显示
        BudgetViewModel.BudgetStatus status = BudgetViewModel.calculateBudgetStatus(
                budgetAmount, usedAmountValue, budgetUsage.alertThreshold);
        updateStatusDisplay(status.statusText, status.statusColor);
    }

    private void calculateCombinedBudgetUsage(List<BudgetDao.BudgetUsage> budgetUsages) {
        BigDecimal totalBudgetAmount = BigDecimal.ZERO;
        BigDecimal totalUsedAmount = BigDecimal.ZERO;
        double averageAlertThreshold = 0.8; // 默认预警阈值

        for (BudgetDao.BudgetUsage usage : budgetUsages) {
            if ("CATEGORY".equals(usage.budgetType)) {
                totalBudgetAmount = totalBudgetAmount.add(usage.budgetAmount != null ? usage.budgetAmount : BigDecimal.ZERO);
                totalUsedAmount = totalUsedAmount.add(usage.usedAmount != null ? usage.usedAmount : BigDecimal.ZERO);
            }
        }

        if (totalBudgetAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 创建虚拟的总预算使用情况
            BudgetDao.BudgetUsage combinedUsage = new BudgetDao.BudgetUsage();
            combinedUsage.budgetAmount = totalBudgetAmount;
            combinedUsage.usedAmount = totalUsedAmount;
            combinedUsage.usagePercentage = totalUsedAmount.divide(totalBudgetAmount, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100")).doubleValue();
            combinedUsage.alertThreshold = averageAlertThreshold;
            combinedUsage.isOverBudget = totalUsedAmount.compareTo(totalBudgetAmount) > 0;

            updateBudgetDisplay(combinedUsage);
        } else {
            // 没有任何预算设置
            updateTotalBudgetDisplay(null);
        }
    }

    private void updateProgressBar(int progressValue, BudgetDao.BudgetUsage budgetUsage) {
        // 确保进度值在有效范围内
        progressValue = Math.max(0, Math.min(100, progressValue));

        Log.d("BudgetWidget", "Updating progress bar to: " + progressValue);

        // 更新颜色（仅当有自定义drawable时）
        updateProgressBarColor(budgetUsage);

        // 直接设置进度值
        budgetProgressBar.setProgress(progressValue);

        Log.d("BudgetWidget", "Progress bar updated successfully");
    }

    private void updateProgressBarColor(BudgetDao.BudgetUsage budgetUsage) {
        int color;
        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);

        if (budgetUsage.isOverBudget) {
            color = Color.RED;
        } else if (remainingPercentage <= ((1 - budgetUsage.alertThreshold) * 100)) {
            // 当剩余预算低于警戒线时（例如剩余不足20%）
            color = Color.parseColor("#FF9800"); // 橙色
        } else {
            color = Color.parseColor("#4CAF50"); // 绿色
        }

        // 安全地更新进度条颜色
        try {
            if (budgetProgressBar.getProgressDrawable() != null) {
                // 使用颜色过滤器更新颜色
                budgetProgressBar.getProgressDrawable().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);
                Log.d("BudgetWidget", "Progress bar color updated to: " + Integer.toHexString(color));
            } else {
                Log.w("BudgetWidget", "Progress drawable is null, cannot update color");
            }
        } catch (Exception e) {
            Log.e("BudgetWidget", "Failed to update progress bar color", e);
        }
    }

    private void createProgressBarDrawable(int progressColor) {
        try {
            // 获取屏幕密度用于dp转px
            float density = getResources().getDisplayMetrics().density;
            int cornerRadius = (int) (4 * density); // 4dp转换为像素

            // 创建背景drawable
            android.graphics.drawable.GradientDrawable backgroundDrawable = new android.graphics.drawable.GradientDrawable();
            backgroundDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
            backgroundDrawable.setCornerRadius(cornerRadius);
            backgroundDrawable.setColor(Color.parseColor("#E0E0E0"));

            // 创建进度drawable
            android.graphics.drawable.GradientDrawable progressDrawable = new android.graphics.drawable.GradientDrawable();
            progressDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
            progressDrawable.setCornerRadius(cornerRadius);
            progressDrawable.setColor(progressColor);

            // 创建ClipDrawable用于进度效果
            android.graphics.drawable.ClipDrawable clipDrawable = new android.graphics.drawable.ClipDrawable(
                    progressDrawable, android.view.Gravity.LEFT, android.graphics.drawable.ClipDrawable.HORIZONTAL);

            // 创建LayerDrawable
            android.graphics.drawable.LayerDrawable layerDrawable = new android.graphics.drawable.LayerDrawable(
                    new android.graphics.drawable.Drawable[]{backgroundDrawable, clipDrawable});

            layerDrawable.setId(0, android.R.id.background);
            layerDrawable.setId(1, android.R.id.progress);

            // 设置到进度条
            budgetProgressBar.setProgressDrawable(layerDrawable);

        } catch (Exception e) {
            // 如果创建drawable失败，使用简单的颜色过滤器
            Log.e("BudgetWidget", "Failed to create progress drawable", e);
            if (budgetProgressBar.getProgressDrawable() != null) {
                budgetProgressBar.getProgressDrawable().setColorFilter(progressColor, android.graphics.PorterDuff.Mode.SRC_IN);
            }
        }
    }

    private void updateStatusDisplay(String status, int color) {
        statusText.setText(status);
        statusText.setTextColor(color);
        statusIndicator.getBackground().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }
}