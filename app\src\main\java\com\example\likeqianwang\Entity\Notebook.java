package com.example.likeqianwang.Entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.example.likeqianwang.Utils.Converters;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

@Entity(
        tableName = "notebooks",
        indices = {
                @Index(value = "notebook_name", unique = true),
                @Index(value = "is_default")
        }
)
@TypeConverters(Converters.class)
public class Notebook implements Serializable {
    @PrimaryKey
    @NonNull
    private String notebookId;

    @ColumnInfo(name = "notebook_name")
    private String notebookName; // 账本名称

    @ColumnInfo(name = "notebook_description")
    private String notebookDescription; // 账本描述

    @ColumnInfo(name = "notebook_icon")
    private int notebookIcon; // 账本图标

    @ColumnInfo(name = "notebook_color")
    private String notebookColor; // 账本颜色

    @ColumnInfo(name = "currency_symbol")
    private String currencySymbol; // 默认币种

    @ColumnInfo(name = "is_default")
    private boolean isDefault; // 是否为默认账本

    @ColumnInfo(name = "is_active")
    private boolean isActive; // 是否启用

    @ColumnInfo(name = "create_time")
    private Date createTime;

    @ColumnInfo(name = "update_time")
    private Date updateTime;

    @ColumnInfo(name = "last_used_time")
    private Date lastUsedTime; // 最后使用时间

    public Notebook() {
        this.notebookId = UUID.randomUUID().toString();
        this.createTime = new Date();
        this.updateTime = new Date();
        this.lastUsedTime = new Date();
        this.currencySymbol = "CNY";
        this.isDefault = false;
        this.isActive = true;
        this.notebookColor = "#4CAF50"; // 默认绿色
    }

    @Ignore
    public Notebook(String notebookName, String notebookDescription) {
        this();
        this.notebookName = notebookName;
        this.notebookDescription = notebookDescription;
    }

    // Getters and Setters
    @NonNull
    public String getNotebookId() {
        return notebookId;
    }

    public void setNotebookId(@NonNull String notebookId) {
        this.notebookId = notebookId;
    }

    public String getNotebookName() {
        return notebookName;
    }

    public void setNotebookName(String notebookName) {
        this.notebookName = notebookName;
    }

    public String getNotebookDescription() {
        return notebookDescription;
    }

    public void setNotebookDescription(String notebookDescription) {
        this.notebookDescription = notebookDescription;
    }

    public int getNotebookIcon() {
        return notebookIcon;
    }

    public void setNotebookIcon(int notebookIcon) {
        this.notebookIcon = notebookIcon;
    }

    public String getNotebookColor() {
        return notebookColor;
    }

    public void setNotebookColor(String notebookColor) {
        this.notebookColor = notebookColor;
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getLastUsedTime() {
        return lastUsedTime;
    }

    public void setLastUsedTime(Date lastUsedTime) {
        this.lastUsedTime = lastUsedTime;
    }

    @Override
    public String toString() {
        return "Notebook{" +
                "notebookId='" + notebookId + '\'' +
                ", notebookName='" + notebookName + '\'' +
                ", notebookDescription='" + notebookDescription + '\'' +
                ", isDefault=" + isDefault +
                ", isActive=" + isActive +
                '}';
    }
}
