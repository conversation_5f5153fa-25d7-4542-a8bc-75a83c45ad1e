-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:2:1-60:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63d128512c76772471e619f21ec79de4\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e76adb6531838024f4d916fe03b677e2\transformed\navigation-common-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaaf952876b0565e2951c4fae286b4d\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3211e138026774bef6b174430e177c1\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b3057b7c0d26ae3cd69c88f8b7ab6e\transformed\navigation-ui-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36a84f081851bde3ce49fbf8d9f79604\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a47c5793c86d0cbdc0a92f639dc30fa\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5750db770b1545548039b6b1f5b37f1\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0be8fe7a00cc17258e0156ab1fb27859\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.56] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3371c2e65acbe1b5dc6a4a339d52e9f\transformed\hilt-android-2.56\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b95ff73c4496a6c930418c8bbf05a7\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2021f0237799f09108b8790c8e46f462\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fea05e3d01849a5ef9fdd534451c932f\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\67fb1f650b0aa61c062f85d5b0a156ab\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\affc042b761f95d4c0a24a1cd7e425c1\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a734b5b213290341ffdc98558d326cb\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b83b02258155c5476b76b3ff4e97146\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d741074cedfe6cba927c1a5317b7915f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2cd110ccf8bf42325d317494e877623\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\63c5db5262b6495c477e00bfe41c11ec\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b27c777c149001cea86d58c3204c8266\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f8e5c456fc1992122c308cea30f743a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac5ce70b3e651702a1d8c2355649c3d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f57a47b5b6fdcd3518a8244a71299ae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f090e541bc6dbb9c3d225994d1adda\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45455740575b476a78bdfa815e6f4546\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1430e30484130ac6a1cc61f394333fa\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1156904667c6a81aac26a8874de0c2ec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\919933fd139c301a7d50d2de8317c5e0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\985ed61f1c5986e19053210ea2dc95d6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\55f05f7e44ab8dad3436e0f46feaef6d\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d36be0189cdfdf67d50b6bb65cedd2e5\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba39fbb461d766894512ca0edf1a7f5\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\0373fd0a1e7c4e38242895545f0af044\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4775ab4134575ec534a2c0a4b17536ab\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d785a681643e9248df8360900dd556be\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7418d897bee78580dd77f26e2d98b01\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\277b036f03004997d02b00623884582a\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef8a6ef88208ddabbad4c88c47d7e47\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4baecc426dc21ce20005bfa17cba605\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c777c839a0842b41cb7dd83e298a5124\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e10f73a8ad24f202781e1e0b347156db\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5bce04cd8aa2cf448b40491e5bcf87d\transformed\flexbox-3.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e64eef7772bbbb07f3c521c1b03f0daf\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c15b94e67876ba2d113b5b8526ce4606\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4232a94526f93ffe459a1f8bb6a34550\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5035484a0f7ac9aec48f52dcb3838e54\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ade7cc33034c5b84dd83726553100e1\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e04c670b5cc850d662f7249cc058eb19\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e722f36a7d0204b1696fa12c8ebd69ca\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa5c0e1a2d9c96b8ff7bf4149d1cc8\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a95358e3a3feeb9e1e015f4d54c13db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\065c1af84ff74f927453cc29d6027676\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8db2d8f2350821072ebafd4295b8c884\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45f3ce8f3933985f4ddff874f04a26a5\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8915658c5d211205996651df19e560c6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\babf1c369313e3d57ebcfe1bd3b48d31\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:dagger-lint-aar:2.56] C:\Users\<USER>\.gradle\caches\8.13\transforms\d836defa4b8d4505084de2982aed79b5\transformed\dagger-lint-aar-2.56\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:6:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:6:22-65
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:7:22-76
application
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:9:5-58:19
INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:9:5-58:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36a84f081851bde3ce49fbf8d9f79604\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36a84f081851bde3ce49fbf8d9f79604\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a47c5793c86d0cbdc0a92f639dc30fa\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a47c5793c86d0cbdc0a92f639dc30fa\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4232a94526f93ffe459a1f8bb6a34550\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4232a94526f93ffe459a1f8bb6a34550\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a95358e3a3feeb9e1e015f4d54c13db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a95358e3a3feeb9e1e015f4d54c13db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:18:9-50
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:12:9-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:10:9-48
activity#com.example.likeqianwang.FillNewAccountInfoActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:20:9-22:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:22:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:21:13-55
activity#com.example.likeqianwang.TagManagementActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:23:9-25:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:25:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:24:13-50
activity#com.example.likeqianwang.ui.budget_settings.BudgetSettingsActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:26:9-28:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:28:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:27:13-70
activity#com.example.likeqianwang.ui.budget_management.BudgetManagementActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:29:9-31:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:31:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:30:13-74
activity#com.example.likeqianwang.ui.category_management.CategoryManagementActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:34:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:33:13-78
activity#com.example.likeqianwang.ui.recurring_transactions.RecurringTransactionsActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:35:9-37:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:37:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:36:13-84
activity#com.example.likeqianwang.RecordingPageActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:38:9-41:59
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:41:13-56
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:40:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:39:13-50
activity#com.example.likeqianwang.MainActivity
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:42:9-51:20
	android:label
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:45:13-45
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:44:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:43:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:46:13-50:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:47:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:47:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:49:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:49:27-74
service#com.example.likeqianwang.service.RecurringTransactionService
ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:54:9-57:40
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:56:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:55:13-64
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63d128512c76772471e619f21ec79de4\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\63d128512c76772471e619f21ec79de4\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e76adb6531838024f4d916fe03b677e2\transformed\navigation-common-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\e76adb6531838024f4d916fe03b677e2\transformed\navigation-common-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaaf952876b0565e2951c4fae286b4d\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aaaf952876b0565e2951c4fae286b4d\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3211e138026774bef6b174430e177c1\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3211e138026774bef6b174430e177c1\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b3057b7c0d26ae3cd69c88f8b7ab6e\transformed\navigation-ui-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b3057b7c0d26ae3cd69c88f8b7ab6e\transformed\navigation-ui-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36a84f081851bde3ce49fbf8d9f79604\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36a84f081851bde3ce49fbf8d9f79604\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a47c5793c86d0cbdc0a92f639dc30fa\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a47c5793c86d0cbdc0a92f639dc30fa\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5750db770b1545548039b6b1f5b37f1\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5750db770b1545548039b6b1f5b37f1\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0be8fe7a00cc17258e0156ab1fb27859\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0be8fe7a00cc17258e0156ab1fb27859\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.56] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3371c2e65acbe1b5dc6a4a339d52e9f\transformed\hilt-android-2.56\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.56] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3371c2e65acbe1b5dc6a4a339d52e9f\transformed\hilt-android-2.56\AndroidManifest.xml:18:3-42
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b95ff73c4496a6c930418c8bbf05a7\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b95ff73c4496a6c930418c8bbf05a7\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2021f0237799f09108b8790c8e46f462\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2021f0237799f09108b8790c8e46f462\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fea05e3d01849a5ef9fdd534451c932f\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fea05e3d01849a5ef9fdd534451c932f\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\67fb1f650b0aa61c062f85d5b0a156ab\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\67fb1f650b0aa61c062f85d5b0a156ab\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\affc042b761f95d4c0a24a1cd7e425c1\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\affc042b761f95d4c0a24a1cd7e425c1\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a734b5b213290341ffdc98558d326cb\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a734b5b213290341ffdc98558d326cb\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b83b02258155c5476b76b3ff4e97146\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b83b02258155c5476b76b3ff4e97146\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d741074cedfe6cba927c1a5317b7915f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d741074cedfe6cba927c1a5317b7915f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2cd110ccf8bf42325d317494e877623\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2cd110ccf8bf42325d317494e877623\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\63c5db5262b6495c477e00bfe41c11ec\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\63c5db5262b6495c477e00bfe41c11ec\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b27c777c149001cea86d58c3204c8266\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b27c777c149001cea86d58c3204c8266\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f8e5c456fc1992122c308cea30f743a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f8e5c456fc1992122c308cea30f743a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac5ce70b3e651702a1d8c2355649c3d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac5ce70b3e651702a1d8c2355649c3d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f57a47b5b6fdcd3518a8244a71299ae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f57a47b5b6fdcd3518a8244a71299ae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f090e541bc6dbb9c3d225994d1adda\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f090e541bc6dbb9c3d225994d1adda\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45455740575b476a78bdfa815e6f4546\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45455740575b476a78bdfa815e6f4546\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1430e30484130ac6a1cc61f394333fa\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1430e30484130ac6a1cc61f394333fa\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1156904667c6a81aac26a8874de0c2ec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1156904667c6a81aac26a8874de0c2ec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\919933fd139c301a7d50d2de8317c5e0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\919933fd139c301a7d50d2de8317c5e0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\985ed61f1c5986e19053210ea2dc95d6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\985ed61f1c5986e19053210ea2dc95d6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\55f05f7e44ab8dad3436e0f46feaef6d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\55f05f7e44ab8dad3436e0f46feaef6d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d36be0189cdfdf67d50b6bb65cedd2e5\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d36be0189cdfdf67d50b6bb65cedd2e5\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba39fbb461d766894512ca0edf1a7f5\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba39fbb461d766894512ca0edf1a7f5\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\0373fd0a1e7c4e38242895545f0af044\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\0373fd0a1e7c4e38242895545f0af044\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4775ab4134575ec534a2c0a4b17536ab\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4775ab4134575ec534a2c0a4b17536ab\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d785a681643e9248df8360900dd556be\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d785a681643e9248df8360900dd556be\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7418d897bee78580dd77f26e2d98b01\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7418d897bee78580dd77f26e2d98b01\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\277b036f03004997d02b00623884582a\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\277b036f03004997d02b00623884582a\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef8a6ef88208ddabbad4c88c47d7e47\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef8a6ef88208ddabbad4c88c47d7e47\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4baecc426dc21ce20005bfa17cba605\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4baecc426dc21ce20005bfa17cba605\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c777c839a0842b41cb7dd83e298a5124\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c777c839a0842b41cb7dd83e298a5124\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e10f73a8ad24f202781e1e0b347156db\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e10f73a8ad24f202781e1e0b347156db\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5bce04cd8aa2cf448b40491e5bcf87d\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5bce04cd8aa2cf448b40491e5bcf87d\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e64eef7772bbbb07f3c521c1b03f0daf\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e64eef7772bbbb07f3c521c1b03f0daf\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c15b94e67876ba2d113b5b8526ce4606\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c15b94e67876ba2d113b5b8526ce4606\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4232a94526f93ffe459a1f8bb6a34550\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4232a94526f93ffe459a1f8bb6a34550\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5035484a0f7ac9aec48f52dcb3838e54\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5035484a0f7ac9aec48f52dcb3838e54\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ade7cc33034c5b84dd83726553100e1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ade7cc33034c5b84dd83726553100e1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e04c670b5cc850d662f7249cc058eb19\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e04c670b5cc850d662f7249cc058eb19\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e722f36a7d0204b1696fa12c8ebd69ca\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e722f36a7d0204b1696fa12c8ebd69ca\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa5c0e1a2d9c96b8ff7bf4149d1cc8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa5c0e1a2d9c96b8ff7bf4149d1cc8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a95358e3a3feeb9e1e015f4d54c13db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a95358e3a3feeb9e1e015f4d54c13db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\065c1af84ff74f927453cc29d6027676\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\065c1af84ff74f927453cc29d6027676\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8db2d8f2350821072ebafd4295b8c884\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8db2d8f2350821072ebafd4295b8c884\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45f3ce8f3933985f4ddff874f04a26a5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45f3ce8f3933985f4ddff874f04a26a5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8915658c5d211205996651df19e560c6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8915658c5d211205996651df19e560c6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\babf1c369313e3d57ebcfe1bd3b48d31\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\babf1c369313e3d57ebcfe1bd3b48d31\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.56] C:\Users\<USER>\.gradle\caches\8.13\transforms\d836defa4b8d4505084de2982aed79b5\transformed\dagger-lint-aar-2.56\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.56] C:\Users\<USER>\.gradle\caches\8.13\transforms\d836defa4b8d4505084de2982aed79b5\transformed\dagger-lint-aar-2.56\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4232a94526f93ffe459a1f8bb6a34550\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4232a94526f93ffe459a1f8bb6a34550\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.example.likeqianwang.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.likeqianwang.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
