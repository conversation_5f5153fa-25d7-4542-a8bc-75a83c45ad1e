package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.Repository.TransactionCategoryRepository;

import java.util.List;

public class TransactionCategoryViewModel extends AndroidViewModel {
    private final TransactionCategoryRepository repository;
    private final MutableLiveData<List<TransactionCategory>> expenseCategories = new MutableLiveData<>();
    private final MutableLiveData<List<TransactionCategory>> incomeCategories = new MutableLiveData<>();
    private final MutableLiveData<String> operationStatus = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    public TransactionCategoryViewModel(@NonNull Application application) {
        super(application);
        repository = new TransactionCategoryRepository(application);
        loadCategories();
    }

    private void loadCategories() {
        // 加载支出分类
        repository.loadCategoriesWithSubcategories(0, categories -> {
            expenseCategories.postValue(categories);
        });

        // 加载收入分类
        repository.loadCategoriesWithSubcategories(1, categories -> {
            incomeCategories.postValue(categories);
        });
    }

    public LiveData<List<TransactionCategory>> getExpenseCategories() {
        return expenseCategories;
    }

    public LiveData<List<TransactionCategory>> getIncomeCategories() {
        return incomeCategories;
    }

    // 根据ID获取分类
    public LiveData<TransactionCategory> getCategoryById(long categoryId) {
        MutableLiveData<TransactionCategory> categoryLiveData = new MutableLiveData<>();
        repository.getCategoryById(categoryId, categoryLiveData::postValue);
        return categoryLiveData;
    }

    // 获取操作状态
    public LiveData<String> getOperationStatus() {
        return operationStatus;
    }

    // 获取错误信息
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    // 根据类型获取分类
    public LiveData<List<TransactionCategory>> getCategoriesByType(int type) {
        MutableLiveData<List<TransactionCategory>> categoriesLiveData = new MutableLiveData<>();
        repository.loadCategoriesWithSubcategories(type, categoriesLiveData::postValue);
        return categoriesLiveData;
    }

    // 插入分类
    public void insertCategory(TransactionCategory category) {
        // TODO: 实现插入分类逻辑
        operationStatus.postValue("分类添加成功");
    }

    // 更新分类
    public void updateCategory(TransactionCategory category) {
        // TODO: 实现更新分类逻辑
        operationStatus.postValue("分类更新成功");
    }

    // 删除分类
    public void deleteCategory(TransactionCategory category) {
        // TODO: 实现删除分类逻辑
        operationStatus.postValue("分类删除成功");
    }

    // 插入子分类
    public void insertSubcategory(TransactionSubcategory subcategory) {
        // TODO: 实现插入子分类逻辑
        operationStatus.postValue("子分类添加成功");
    }

    // 更新子分类
    public void updateSubcategory(TransactionSubcategory subcategory) {
        // TODO: 实现更新子分类逻辑
        operationStatus.postValue("子分类更新成功");
    }

    // 删除子分类
    public void deleteSubcategory(TransactionSubcategory subcategory) {
        // TODO: 实现删除子分类逻辑
        operationStatus.postValue("子分类删除成功");
    }
}
