1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.likeqianwang"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="35"
9        android:targetSdkVersion="35" />
10
11    <!-- 周期记账服务需要的权限 -->
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:6:5-68
12-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.likeqianwang.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.likeqianwang.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:9:5-58:19
22        android:name="com.example.likeqianwang.LikeQianWangApplication"
22-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:10:9-48
23        android:allowBackup="true"
23-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:17:9-35
33        android:theme="@style/Theme.LikeQianWang" >
33-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:18:9-50
34        <activity
34-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:20:9-22:40
35            android:name="com.example.likeqianwang.FillNewAccountInfoActivity"
35-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:21:13-55
36            android:exported="false" />
36-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:22:13-37
37        <activity
37-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:23:9-25:40
38            android:name="com.example.likeqianwang.TagManagementActivity"
38-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:24:13-50
39            android:exported="false" />
39-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:25:13-37
40        <activity
40-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:26:9-28:40
41            android:name="com.example.likeqianwang.ui.budget_settings.BudgetSettingsActivity"
41-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:27:13-70
42            android:exported="false" />
42-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:28:13-37
43        <activity
43-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:29:9-31:40
44            android:name="com.example.likeqianwang.ui.budget_management.BudgetManagementActivity"
44-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:30:13-74
45            android:exported="false" />
45-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:31:13-37
46        <activity
46-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:32:9-34:40
47            android:name="com.example.likeqianwang.ui.category_management.CategoryManagementActivity"
47-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:33:13-78
48            android:exported="false" />
48-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:34:13-37
49        <activity
49-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:35:9-37:40
50            android:name="com.example.likeqianwang.ui.recurring_transactions.RecurringTransactionsActivity"
50-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:36:13-84
51            android:exported="false" />
51-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:37:13-37
52        <activity
52-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:38:9-41:59
53            android:name="com.example.likeqianwang.RecordingPageActivity"
53-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:39:13-50
54            android:exported="false"
54-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:40:13-37
55            android:windowSoftInputMode="adjustNothing" />
55-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:41:13-56
56        <activity
56-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:42:9-51:20
57            android:name="com.example.likeqianwang.MainActivity"
57-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:43:13-41
58            android:exported="true"
58-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:44:13-36
59            android:label="@string/app_name" >
59-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:45:13-45
60            <intent-filter>
60-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:46:13-50:29
61                <action android:name="android.intent.action.MAIN" />
61-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:47:17-69
61-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:47:25-66
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:49:17-77
63-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:49:27-74
64            </intent-filter>
65        </activity>
66
67        <!-- 周期记账服务 -->
68        <service
68-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:54:9-57:40
69            android:name="com.example.likeqianwang.service.RecurringTransactionService"
69-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:55:13-64
70            android:enabled="true"
70-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:56:13-35
71            android:exported="false" />
71-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:57:13-37
72
73        <provider
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
74            android:name="androidx.startup.InitializationProvider"
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
75            android:authorities="com.example.likeqianwang.androidx-startup"
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
76            android:exported="false" >
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
77            <meta-data
77-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.emoji2.text.EmojiCompatInitializer"
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
79                android:value="androidx.startup" />
79-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
81-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
82                android:value="androidx.startup" />
82-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
83            <meta-data
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
84                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
85                android:value="androidx.startup" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
86        </provider>
87
88        <uses-library
88-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
89            android:name="androidx.window.extensions"
89-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
90            android:required="false" />
90-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
91        <uses-library
91-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
92            android:name="androidx.window.sidecar"
92-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
93            android:required="false" />
93-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
94
95        <service
95-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
96            android:name="androidx.room.MultiInstanceInvalidationService"
96-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
97            android:directBootAware="true"
97-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
98            android:exported="false" />
98-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
99
100        <receiver
100-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
101            android:name="androidx.profileinstaller.ProfileInstallReceiver"
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
102            android:directBootAware="false"
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
103            android:enabled="true"
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
104            android:exported="true"
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
105            android:permission="android.permission.DUMP" >
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
107                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
110                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
113                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
116                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
117            </intent-filter>
118        </receiver>
119    </application>
120
121</manifest>
