<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/widget_common_bg"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/category_edit_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="新增分类"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/category_edit_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/receipt_关闭"
            android:src="@drawable/ic_close"
            app:tint="@color/grey" />

    </LinearLayout>

    <!-- 分类名称 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="分类名称"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/category_edit_name"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/style_edittext_bg"
            android:hint="请输入分类名称"
            android:inputType="text"
            android:maxLength="20"
            android:paddingHorizontal="16dp"
            android:textColor="@color/black"
            android:textColorHint="@color/grey"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 分类类型 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="分类类型"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <RadioGroup
            android:id="@+id/category_edit_type_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/category_edit_expense"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="支出"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RadioButton
                android:id="@+id/category_edit_income"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="收入"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RadioButton
                android:id="@+id/category_edit_transfer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转账"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </RadioGroup>

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/category_edit_cancel"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/button_outline_grey"
            android:text="取消"
            android:textColor="@color/grey"
            android:textSize="16sp" />

        <Button
            android:id="@+id/category_edit_save"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/button_primary"
            android:text="保存"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>
